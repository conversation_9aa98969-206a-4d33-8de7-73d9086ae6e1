<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>MindAR AR Responsive</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no" />

    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/donmccurdy/aframe-extras@v7.0.0/dist/aframe-extras.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mind-ar@1.2.5/dist/mindar-image-aframe.prod.js"></script>

    <style>
      html, body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        background: black;
        position: fixed;
        top: 0;
        left: 0;
      }

      a-scene {
        width: 100% !important;
        height: 100% !important;
        position: fixed !important;
        top: 0;
        left: 0;
        margin: 0;
        padding: 0;
      }

      .a-enter-vr {
        display: none !important;
      }

      .control-button, .google-button {
        position: absolute;
        z-index: 20;
        font-weight: bold;
        cursor: pointer;
        transform-origin: center;
      }

      .control-button {
        top: 10px;
        right: 10px;
        background-color: rgba(255, 255, 255, 0.7);
        border-radius: 5px;
        padding: 8px 12px;
        display: none;
      }

      .google-button {
        background-color: #4285F4;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 10px 15px;
        display: none;
      }
    </style>
  </head>

  <body>
    <a-scene
      mindar-image="imageTargetSrc: https://FAiRarWeb.github.io/project123/targets.mind;
                    uiScanning: true;
                    filterMinCF: 0.0005;
                    filterBeta: 0.01;
                    missTolerance: 3;
                    warmupTolerance: 2;"
      vr-mode-ui="enabled: false"
      device-orientation-permission-ui="enabled: false"
      renderer="precision: high; antialias: true"
      embedded
    >
      <a-assets>
        <video id="video1" src="https://FAiRarWeb.github.io/project123/videoAR.mp4" preload="auto" loop crossorigin="anonymous"></video>
      </a-assets>

      <a-camera position="0 0 0" look-controls="enabled: false"></a-camera>

      <a-entity mindar-image-target="targetIndex: 0" videohandler>
        <a-video
          src="#video1"
          position="0 0 0"
          width="1"
          height="0.45"
          autoplay="false"
          loop="true"
          visible="false"
        ></a-video>
      </a-entity>
    </a-scene>

    <button id="toggleButton" class="control-button">Show Button</button>
    <button id="googleButton" class="google-button" onclick="window.open('https://www.google.com', '_blank')">i</button>

    <script>
      AFRAME.registerComponent("videohandler", {
        init: function () {
          const videoEl = document.querySelector("#video1");
          const videoPlane = this.el.querySelector("a-video");
          const toggleButton = document.querySelector("#toggleButton");
          const googleButton = document.querySelector("#googleButton");
          const camera = document.querySelector("a-camera").object3D;
          
          // Track device orientation
          let deviceOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
          
          // Device orientation detection
          window.addEventListener("deviceorientation", (event) => {
            // Get gamma (rotation around front-to-back axis)
            const gamma = Math.abs(event.gamma);
            
            // Determine orientation based on gamma angle
            const newOrientation = (gamma > 45 && gamma < 135) ? 'landscape' : 'portrait';
            
            // Only update if orientation changed
            if (newOrientation !== deviceOrientation) {
              deviceOrientation = newOrientation;
              updateLayout(deviceOrientation);
            }
          }, true);
          
          const updateLayout = (orientation) => {
            console.log("Device orientation:", orientation);
            
            if (orientation === 'landscape') {
              // Keep video in original orientation (no rotation)
              videoPlane.setAttribute("rotation", "0 0 0");
              
              // Move toggle button to bottom right and rotate
              toggleButton.style.top = "auto";
              toggleButton.style.bottom = "80px";
              toggleButton.style.right = "10px";
              toggleButton.style.transform = "rotate(90deg)";
              
              // Rotate Google button
              googleButton.style.transform = "rotate(90deg)";
            } else {
              // Keep original rotation in portrait mode
              videoPlane.setAttribute("rotation", "0 0 0");
              
              // Reset toggle button position to top right
              toggleButton.style.top = "10px";
              toggleButton.style.bottom = "auto";
              toggleButton.style.right = "10px";
              toggleButton.style.transform = "rotate(0deg)";
              
              // Reset Google button rotation
              googleButton.style.transform = "translate(-50%, -50%) scale(1)";
            }
          };
          
          // Initialize layout based on current orientation
          updateLayout(deviceOrientation);
          
          this.el.addEventListener("targetFound", () => {
            console.log("Target found");
            videoPlane.setAttribute("visible", true);
            videoEl.play().catch(e => console.warn("Autoplay blocked:", e));
            toggleButton.style.display = "block";
            
            // Apply current orientation when target is found
            updateLayout(deviceOrientation);
          });

          this.el.addEventListener("targetLost", () => {
            console.log("Target lost");
            videoEl.pause();
            videoPlane.setAttribute("visible", false);
            toggleButton.style.display = "none";
            googleButton.style.display = "none";
          });

          toggleButton.addEventListener("click", () => {
            const isHidden = googleButton.style.display === "none" || googleButton.style.display === "";
            googleButton.style.display = isHidden ? "block" : "none";
            toggleButton.textContent = isHidden ? "Hide Button" : "Show Button";
          });

          this.tick = () => {
            if (googleButton.style.display !== "block" || !videoPlane.getAttribute("visible")) return;

            const videoWorldPosition = new THREE.Vector3();
            videoPlane.object3D.getWorldPosition(videoWorldPosition);

            const vector = videoWorldPosition.clone();
            vector.project(camera.children[0]);

            const widthHalf = window.innerWidth / 2;
            const heightHalf = window.innerHeight / 2;
            const x = (vector.x * widthHalf) + widthHalf;
            const y = -(vector.y * heightHalf) + heightHalf;

            const distance = camera.position.distanceTo(videoWorldPosition);
            const scaleFactor = Math.max(0.5, Math.min(1.5, 1 / Math.max(0.1, distance)));

            // Adjust position based on orientation
            if (deviceOrientation === 'landscape') {
              // Offset the button position to account for rotation
              googleButton.style.left = `${x + 10}px`;  // Adjust these offset values as needed
              googleButton.style.top = `${y - 90}px`;   // Adjust these offset values as needed
            } else {
              googleButton.style.left = `${x}px`;
              googleButton.style.top = `${y}px`;
            }
            
            // Apply rotation based on current orientation along with scaling
            const rotation = deviceOrientation === 'landscape' ? 'rotate(90deg)' : 'rotate(0deg)';
            googleButton.style.transform = `translate(-50%, -50%) scale(${scaleFactor}) ${rotation}`;
          };
        },
      });
    </script>
  </body>
</html>