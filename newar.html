<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>MindAR Project</title>

    <!-- MindAR + A-Frame Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/aframe@1.4.2/dist/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mind-ar@1.1.7/dist/mindar-image-aframe.prod.js"></script>
    <style>
      body {
        margin: 0;
        overflow: hidden;
        font-family: Arial, sans-serif;
      }

      #startup-screen {
        position: absolute;
        width: 100%;
        height: 100%;
        background: #111;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        color: white;
        transition: opacity 0.5s ease;
      }

      #startup-screen.hidden {
        opacity: 0;
        pointer-events: none;
      }

      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-top: 5px solid #fff;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 20px;
      }

      .checkmark-container {
        font-size: 48px;
        color: limegreen;
        display: none;
        margin-bottom: 20px;
      }

      .checkmark-container.show {
        display: block;
      }

      #loading-text {
        font-size: 18px;
        margin-bottom: 20px;
      }

      #loading-text.ready {
        color: limegreen;
      }

      #start-button {
        padding: 10px 20px;
        font-size: 18px;
        background-color: #4caf50;
        color: white;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.5s;
      }

      #start-button.show {
        opacity: 1;
      }

      .action-buttons {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
      }

      .action-buttons button {
        margin-left: 10px;
        padding: 8px 12px;
        background: #fff;
        border: 1px solid #ccc;
        border-radius: 4px;
        cursor: pointer;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <!-- Startup Loading Screen -->
    <div id="startup-screen">
      <div class="loading-spinner" id="loading-spinner"></div>
      <div class="checkmark-container" id="checkmark-container">✔</div>
      <div id="loading-text">Loading assets...</div>
      <button id="start-button" onclick="startARExperience()">Start Experience</button>
    </div>

    <!-- Action Buttons -->
    <div class="action-buttons">
      <button onclick="toggleAnimation()">Animate Me</button>
      <button onclick="toggleInfo()">Info</button>
    </div>

    <!-- AR Scene -->
    <a-scene
      id="ar-scene"
      mindar-image="imageTargetSrc: https://cdn.jsdelivr.net/gh/fairarweb/project/targets.mind;"
      embedded
      color-space="sRGB"
      renderer="colorManagement: true, physicallyCorrectLights"
      vr-mode-ui="enabled: false"
      device-orientation-permission-ui="enabled: false"
      style="display: none"
    >
      <a-assets>
        <video id="ar-video" src="https://cdn.jsdelivr.net/gh/fairarweb/project/sample.mp4" preload="auto" loop="true" muted playsinline crossorigin="anonymous"></video>
      </a-assets>

      <!-- Image Target 0 -->
      <a-entity mindar-image-target="targetIndex: 0">
        <a-video
          src="#ar-video"
          width="1"
          height="0.6"
          position="0 0 0"
          rotation="0 0 0"
          autoplay="true"
          loop="true"
        ></a-video>
        <a-text
          class="target-text"
          value="Scan this poster to watch video"
          color="white"
          position="0 0.6 0"
          scale="0.5 0.5 0.5"
        ></a-text>
        <a-entity
          class="website-button"
          position="0.35 0.25 0.1"
          scale="0.4 0.4 0.4"
          visible="false"
          cursor-listener
        >
          <a-box
            position="0 0 0"
            width="0.6"
            height="0.15"
            depth="0.05"
            color="#4caf50"
            material="opacity: 0.9"
            animation__hover="property: scale; to: 1.2 1.2 1.2; startEvents: mouseenter; dur: 200"
            animation__unhover="property: scale; to: 1 1 1; startEvents: mouseleave; dur: 200"
          ></a-box>
          <a-text
            value="Visit Web"
            position="0 0 0.026"
            align="center"
            color="white"
            scale="1 1 1"
            font="roboto"
          ></a-text>
        </a-entity>
      </a-entity>

      <!-- Image Target 1 -->
      <a-entity mindar-image-target="targetIndex: 1">
        <a-video
          src="#ar-video"
          width="1"
          height="0.6"
          position="0 0 0"
          rotation="0 0 0"
          autoplay="true"
          loop="true"
        ></a-video>
        <a-text
          class="target-text"
          value="Scan this poster to watch video"
          color="white"
          position="0 0.6 0"
          scale="0.5 0.5 0.5"
        ></a-text>
        <a-entity
          class="website-button"
          position="0.35 0.25 0.1"
          scale="0.4 0.4 0.4"
          visible="false"
          cursor-listener
        >
          <a-box
            position="0 0 0"
            width="0.6"
            height="0.15"
            depth="0.05"
            color="#4caf50"
            material="opacity: 0.9"
            animation__hover="property: scale; to: 1.2 1.2 1.2; startEvents: mouseenter; dur: 200"
            animation__unhover="property: scale; to: 1 1 1; startEvents: mouseleave; dur: 200"
          ></a-box>
          <a-text
            value="Visit Web"
            position="0 0 0.026"
            align="center"
            color="white"
            scale="1 1 1"
            font="roboto"
          ></a-text>
        </a-entity>
      </a-entity>
    </a-scene>

    <!-- JavaScript Logic -->
    <script>
      const startButton = document.getElementById("start-button");
      const loadingSpinner = document.getElementById("loading-spinner");
      const checkmarkContainer = document.getElementById("checkmark-container");
      const loadingText = document.getElementById("loading-text");
      const startupScreen = document.getElementById("startup-screen");
      const arScene = document.getElementById("ar-scene");

      const video = document.getElementById("ar-video");
      const videoPlanes = document.querySelectorAll("a-video");

      function startARExperience() {
        startupScreen.classList.add("hidden");
        arScene.style.display = "block";
        videoPlanes.forEach((plane) => {
          plane.setAttribute("src", "#ar-video");
        });
        video.play();
      }

      // Simulate loading delay
      window.addEventListener("load", () => {
        setTimeout(() => {
          loadingSpinner.classList.add("hidden");
          checkmarkContainer.classList.add("show");
          loadingText.textContent = "Ready!";
          loadingText.classList.add("ready");
          startButton.classList.add("show");
        }, 2500);
      });

      // Toggle animation on all targets
      function toggleAnimation() {
        const allTargets = document.querySelectorAll("[mindar-image-target]");
        allTargets.forEach((target) => {
          target.object3D.rotation.y += Math.PI / 4;
        });
      }

      // Toggle info button visibility
      function toggleInfo() {
        const buttons = document.querySelectorAll(".website-button");
        buttons.forEach((btn) => {
          btn.setAttribute("visible", btn.getAttribute("visible") !== "true");
        });
      }

      // Cursor listener to open website
      AFRAME.registerComponent("cursor-listener", {
        init: function () {
          this.el.addEventListener("click", function () {
            window.open("https://fairarweb.github.io", "_blank");
          });
        },
      });
    </script>
  </body>
</html>